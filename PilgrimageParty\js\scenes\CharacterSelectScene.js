import { BaseScene } from '../core/SceneManager';
import { SCENES, CHARACTERS } from '../config/GameConfig';

/**
 * 角色选择场景
 */
export default class CharacterSelectScene extends BaseScene {
  constructor() {
    super();
    this.selectedCharacter = null;
    this.characterButtons = [];
    this.animationTime = 0; // 用于动画效果
    this.showSkills = false; // 是否显示技能详情
    this.skillAnimationTime = 0; // 技能动画时间
    this.initCharacterButtons();
  }

  /**
   * 初始化角色按钮
   */
  initCharacterButtons() {
    const characters = Object.values(CHARACTERS);
    const buttonWidth = 150;
    const buttonHeight = 200;
    const spacing = 20;
    const totalWidth = characters.length * buttonWidth + (characters.length - 1) * spacing;
    const startX = (canvas.width - totalWidth) / 2;
    const startY = 200;

    this.characterButtons = characters.map((character, index) => ({
      character: character,
      x: startX + index * (buttonWidth + spacing),
      y: startY,
      width: buttonWidth,
      height: buttonHeight,
      selected: false
    }));
  }

  /**
   * 场景进入时调用
   */
  onEnter(params = {}) {
    super.onEnter(params);
    console.log('进入角色选择场景');
  }

  /**
   * 更新场景逻辑
   */
  update(deltaTime) {
    // 更新动画时间
    this.animationTime += deltaTime;

    // 如果显示技能，更新技能动画
    if (this.showSkills) {
      this.skillAnimationTime += deltaTime;
    }
  }

  /**
   * 渲染场景
   */
  render(ctx) {
    // 绘制背景
    this.renderBackground(ctx);

    // 绘制标题
    this.renderTitle(ctx);

    // 绘制角色选项
    this.renderCharacters(ctx);

    // 绘制角色详细信息
    if (this.selectedCharacter) {
      this.renderCharacterDetails(ctx);
    }

    // 绘制确认按钮
    this.renderConfirmButton(ctx);

    // 绘制返回按钮
    this.renderBackButton(ctx);
  }

  /**
   * 绘制背景
   */
  renderBackground(ctx) {
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#FFE4B5');
    gradient.addColorStop(1, '#DEB887');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }

  /**
   * 绘制标题
   */
  renderTitle(ctx) {
    ctx.save();
    ctx.fillStyle = '#8B4513';
    ctx.font = 'bold 36px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('选择角色', canvas.width / 2, 100);
    ctx.restore();
  }

  /**
   * 绘制角色选项
   */
  renderCharacters(ctx) {
    this.characterButtons.forEach(button => {
      this.renderCharacterButton(ctx, button);
    });
  }

  /**
   * 绘制角色按钮
   */
  renderCharacterButton(ctx, button) {
    ctx.save();
    
    // 绘制按钮背景
    ctx.fillStyle = button.selected ? '#FFD700' : '#F5DEB3';
    ctx.strokeStyle = button.selected ? '#FF8C00' : '#8B4513';
    ctx.lineWidth = button.selected ? 4 : 2;
    
    ctx.fillRect(button.x, button.y, button.width, button.height);
    ctx.strokeRect(button.x, button.y, button.width, button.height);
    
    // 绘制角色名称
    ctx.fillStyle = '#8B4513';
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    
    ctx.fillText(
      button.character.name,
      button.x + button.width / 2,
      button.y + 10
    );
    
    // 绘制武器信息
    ctx.font = '16px Arial';
    ctx.fillText(
      button.character.weapon,
      button.x + button.width / 2,
      button.y + 40
    );
    
    // 绘制基础属性
    const stats = button.character.baseStats;
    const statY = button.y + 80;
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    
    ctx.fillText(`生命: ${stats.hp}`, button.x + 10, statY);
    ctx.fillText(`攻击: ${stats.attack}`, button.x + 10, statY + 20);
    ctx.fillText(`防御: ${stats.defense}`, button.x + 10, statY + 40);
    ctx.fillText(`速度: ${stats.speed}`, button.x + 10, statY + 60);
    
    ctx.restore();
  }

  /**
   * 绘制确认按钮
   */
  renderConfirmButton(ctx) {
    if (!this.selectedCharacter) return;
    
    ctx.save();
    
    const buttonX = canvas.width / 2 - 100;
    const buttonY = 500;
    const buttonWidth = 200;
    const buttonHeight = 50;
    
    ctx.fillStyle = '#32CD32';
    ctx.strokeStyle = '#228B22';
    ctx.lineWidth = 3;
    
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
    ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);
    
    ctx.fillStyle = 'white';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('确认选择', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);
    
    ctx.restore();
  }

  /**
   * 绘制返回按钮
   */
  renderBackButton(ctx) {
    ctx.save();
    
    const buttonX = 20;
    const buttonY = 20;
    const buttonWidth = 80;
    const buttonHeight = 40;
    
    ctx.fillStyle = '#DC143C';
    ctx.strokeStyle = '#8B0000';
    ctx.lineWidth = 2;
    
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
    ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);
    
    ctx.fillStyle = 'white';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('返回', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);
    
    ctx.restore();
  }

  /**
   * 处理触摸事件
   */
  handleTouch(event) {
    if (event.type === 'touchstart' || event.type === 'click') {
      // 检查角色按钮点击
      this.characterButtons.forEach(button => {
        if (this.isPointInRect(event.x, event.y, button)) {
          // 取消其他角色的选择
          this.characterButtons.forEach(b => b.selected = false);
          
          // 选择当前角色
          button.selected = true;
          this.selectedCharacter = button.character;
          
          if (GameGlobal.musicManager) {
            GameGlobal.musicManager.playSfx('click');
          }
        }
      });
      
      // 检查确认按钮点击
      if (this.selectedCharacter && this.isPointInRect(event.x, event.y, {
        x: canvas.width / 2 - 100,
        y: 500,
        width: 200,
        height: 50
      })) {
        // 保存选择的角色
        GameGlobal.databus.currentPlayer = {
          character: this.selectedCharacter,
          level: 1,
          exp: 0,
          equipment: {},
          skills: []
        };
        
        // 切换到关卡选择场景
        this.switchScene(SCENES.LEVEL_SELECT);
      }
      
      // 检查返回按钮点击
      if (this.isPointInRect(event.x, event.y, {
        x: 20,
        y: 20,
        width: 80,
        height: 40
      })) {
        this.switchScene(SCENES.MENU);
      }
    }
  }

  /**
   * 检查点是否在矩形内
   */
  isPointInRect(x, y, rect) {
    return x >= rect.x && 
           x <= rect.x + rect.width && 
           y >= rect.y && 
           y <= rect.y + rect.height;
  }
}
