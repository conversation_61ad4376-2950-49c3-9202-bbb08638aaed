import { BaseScene } from '../core/SceneManager';
import { SCENES } from '../config/GameConfig';

/**
 * 战斗场景
 */
export default class BattleScene extends BaseScene {
  constructor() {
    super();
    this.currentLevel = null;
    this.enemies = [];
    this.battlePhase = 'preparation'; // preparation, battle, victory, defeat
  }

  /**
   * 场景进入时调用
   */
  onEnter(params = {}) {
    super.onEnter(params);
    
    this.currentLevel = params.level;
    this.battlePhase = 'preparation';
    
    // 初始化战斗
    this.initBattle();
    
    console.log(`进入战斗场景: ${params.level?.name}`);
  }

  /**
   * 初始化战斗
   */
  initBattle() {
    // 根据关卡生成敌人
    this.generateEnemies();
    
    // 进入战斗状态
    GameGlobal.databus.enterBattle(this.enemies);
  }

  /**
   * 生成敌人
   */
  generateEnemies() {
    // 临时实现：根据关卡难度生成敌人
    const difficulty = this.currentLevel?.difficulty || 1;
    const enemyCount = Math.min(difficulty + 1, 4);
    
    this.enemies = [];
    for (let i = 0; i < enemyCount; i++) {
      this.enemies.push({
        id: `enemy_${i}`,
        name: `妖怪${i + 1}`,
        hp: 50 + difficulty * 10,
        maxHp: 50 + difficulty * 10,
        attack: 10 + difficulty * 2,
        defense: 5 + difficulty,
        x: 100 + i * 80,
        y: 150,
        alive: true
      });
    }
  }

  /**
   * 更新场景逻辑
   */
  update(deltaTime) {
    if (this.battlePhase === 'battle') {
      // 更新战斗逻辑
      this.updateBattle(deltaTime);
    }
  }

  /**
   * 更新战斗逻辑
   */
  updateBattle(deltaTime) {
    // 检查战斗结束条件
    const aliveEnemies = this.enemies.filter(enemy => enemy.alive);
    
    if (aliveEnemies.length === 0) {
      this.battlePhase = 'victory';
      this.onBattleVictory();
    }
    
    // 检查玩家是否失败
    const player = GameGlobal.databus.currentPlayer;
    if (player && player.hp <= 0) {
      this.battlePhase = 'defeat';
      this.onBattleDefeat();
    }
  }

  /**
   * 战斗胜利
   */
  onBattleVictory() {
    console.log('战斗胜利！');
    
    // 解锁下一关
    const nextLevelId = this.currentLevel.id + 1;
    const nextChapter = Math.ceil(nextLevelId / 9);
    const nextLevel = ((nextLevelId - 1) % 9) + 1;
    
    GameGlobal.databus.unlockLevel(nextChapter, nextLevel);
    
    // 播放胜利音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playSfx('victory');
    }
  }

  /**
   * 战斗失败
   */
  onBattleDefeat() {
    console.log('战斗失败！');
    
    // 播放失败音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playSfx('defeat');
    }
  }

  /**
   * 渲染场景
   */
  render(ctx) {
    this.renderBackground(ctx);
    this.renderBattleInfo(ctx);
    this.renderEnemies(ctx);
    this.renderPlayer(ctx);
    this.renderUI(ctx);
  }

  /**
   * 绘制背景
   */
  renderBackground(ctx) {
    // 战斗背景
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#8B4513');
    gradient.addColorStop(1, '#D2691E');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }

  /**
   * 绘制战斗信息
   */
  renderBattleInfo(ctx) {
    if (!this.currentLevel) return;
    
    ctx.save();
    ctx.fillStyle = 'white';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    
    ctx.fillText(this.currentLevel.name, canvas.width / 2, 20);
    ctx.restore();
  }

  /**
   * 绘制敌人
   */
  renderEnemies(ctx) {
    this.enemies.forEach(enemy => {
      if (enemy.alive) {
        this.renderEnemy(ctx, enemy);
      }
    });
  }

  /**
   * 绘制单个敌人
   */
  renderEnemy(ctx, enemy) {
    ctx.save();
    
    // 绘制敌人
    ctx.fillStyle = '#8B0000';
    ctx.fillRect(enemy.x, enemy.y, 60, 80);
    
    // 绘制敌人名称
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(enemy.name, enemy.x + 30, enemy.y - 10);
    
    // 绘制血条
    const barWidth = 60;
    const barHeight = 8;
    const hpRatio = enemy.hp / enemy.maxHp;
    
    // 血条背景
    ctx.fillStyle = '#333333';
    ctx.fillRect(enemy.x, enemy.y + 85, barWidth, barHeight);
    
    // 血条
    ctx.fillStyle = hpRatio > 0.5 ? '#00FF00' : hpRatio > 0.2 ? '#FFFF00' : '#FF0000';
    ctx.fillRect(enemy.x, enemy.y + 85, barWidth * hpRatio, barHeight);
    
    ctx.restore();
  }

  /**
   * 绘制玩家
   */
  renderPlayer(ctx) {
    const player = GameGlobal.databus.currentPlayer;
    if (!player) return;
    
    ctx.save();
    
    // 绘制玩家
    const playerX = canvas.width / 2 - 30;
    const playerY = 400;
    
    ctx.fillStyle = '#0000FF';
    ctx.fillRect(playerX, playerY, 60, 80);
    
    // 绘制玩家名称
    ctx.fillStyle = 'white';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(player.character.name, playerX + 30, playerY - 15);
    
    ctx.restore();
  }

  /**
   * 绘制UI
   */
  renderUI(ctx) {
    this.renderBattleButtons(ctx);
    this.renderBattlePhaseInfo(ctx);
  }

  /**
   * 绘制战斗按钮
   */
  renderBattleButtons(ctx) {
    if (this.battlePhase === 'preparation') {
      // 开始战斗按钮
      ctx.save();
      
      const buttonX = canvas.width / 2 - 75;
      const buttonY = 550;
      const buttonWidth = 150;
      const buttonHeight = 50;
      
      ctx.fillStyle = '#32CD32';
      ctx.strokeStyle = '#228B22';
      ctx.lineWidth = 3;
      
      ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
      ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);
      
      ctx.fillStyle = 'white';
      ctx.font = 'bold 20px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      ctx.fillText('开始战斗', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);
      
      ctx.restore();
    } else if (this.battlePhase === 'victory' || this.battlePhase === 'defeat') {
      // 返回按钮
      ctx.save();
      
      const buttonX = canvas.width / 2 - 75;
      const buttonY = 550;
      const buttonWidth = 150;
      const buttonHeight = 50;
      
      ctx.fillStyle = '#4169E1';
      ctx.strokeStyle = '#191970';
      ctx.lineWidth = 3;
      
      ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
      ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);
      
      ctx.fillStyle = 'white';
      ctx.font = 'bold 20px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      ctx.fillText('返回', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);
      
      ctx.restore();
    }
  }

  /**
   * 绘制战斗阶段信息
   */
  renderBattlePhaseInfo(ctx) {
    let message = '';
    let color = 'white';
    
    switch (this.battlePhase) {
      case 'preparation':
        message = '准备战斗';
        break;
      case 'battle':
        message = '战斗中...';
        color = '#FFFF00';
        break;
      case 'victory':
        message = '胜利！';
        color = '#00FF00';
        break;
      case 'defeat':
        message = '失败！';
        color = '#FF0000';
        break;
    }
    
    ctx.save();
    ctx.fillStyle = color;
    ctx.font = 'bold 28px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText(message, canvas.width / 2, 350);
    ctx.restore();
  }

  /**
   * 处理触摸事件
   */
  handleTouch(event) {
    if (event.type === 'touchstart' || event.type === 'click') {
      if (this.battlePhase === 'preparation') {
        // 检查开始战斗按钮
        if (this.isPointInRect(event.x, event.y, {
          x: canvas.width / 2 - 75,
          y: 550,
          width: 150,
          height: 50
        })) {
          this.battlePhase = 'battle';
          
          if (GameGlobal.musicManager) {
            GameGlobal.musicManager.playSfx('click');
          }
        }
      } else if (this.battlePhase === 'victory' || this.battlePhase === 'defeat') {
        // 检查返回按钮
        if (this.isPointInRect(event.x, event.y, {
          x: canvas.width / 2 - 75,
          y: 550,
          width: 150,
          height: 50
        })) {
          GameGlobal.databus.exitBattle();
          this.switchScene(SCENES.LEVEL_SELECT);
        }
      }
    }
  }

  /**
   * 检查点是否在矩形内
   */
  isPointInRect(x, y, rect) {
    return x >= rect.x && 
           x <= rect.x + rect.width && 
           y >= rect.y && 
           y <= rect.y + rect.height;
  }
}
