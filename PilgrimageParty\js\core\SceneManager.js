/**
 * 场景管理器
 * 负责管理游戏中的各种场景切换和渲染
 */
export default class SceneManager {
  constructor() {
    this.scenes = new Map(); // 存储所有场景
    this.currentScene = null; // 当前活跃场景
    this.isTransitioning = false; // 是否正在切换场景
  }

  /**
   * 注册场景
   * @param {string} name - 场景名称
   * @param {Object} scene - 场景对象
   */
  registerScene(name, scene) {
    this.scenes.set(name, scene);
    scene.name = name;
    scene.sceneManager = this;
  }

  /**
   * 切换到指定场景
   * @param {string} sceneName - 目标场景名称
   * @param {Object} params - 传递给场景的参数
   */
  switchTo(sceneName, params = {}) {
    if (this.isTransitioning) {
      console.warn('场景正在切换中，请稍后再试');
      return;
    }

    const targetScene = this.scenes.get(sceneName);
    if (!targetScene) {
      console.error(`场景 ${sceneName} 不存在`);
      return;
    }

    this.isTransitioning = true;

    // 退出当前场景
    if (this.currentScene) {
      this.currentScene.onExit();
    }

    // 进入新场景
    this.currentScene = targetScene;
    this.currentScene.onEnter(params);

    // 更新全局状态
    GameGlobal.databus.switchScene(sceneName);

    this.isTransitioning = false;
  }

  /**
   * 获取当前场景
   */
  getCurrentScene() {
    return this.currentScene;
  }

  /**
   * 更新当前场景
   * @param {number} deltaTime - 时间间隔
   */
  update(deltaTime) {
    if (this.currentScene && this.currentScene.update) {
      this.currentScene.update(deltaTime);
    }
  }

  /**
   * 渲染当前场景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    if (this.currentScene && this.currentScene.render) {
      this.currentScene.render(ctx);
    }
  }

  /**
   * 处理触摸事件
   * @param {Object} event - 触摸事件
   */
  handleTouch(event) {
    if (this.currentScene && this.currentScene.handleTouch) {
      this.currentScene.handleTouch(event);
    }
  }

  /**
   * 销毁场景管理器
   */
  destroy() {
    if (this.currentScene) {
      this.currentScene.onExit();
    }
    this.scenes.clear();
    this.currentScene = null;
  }
}

/**
 * 基础场景类
 * 所有场景都应该继承这个类
 */
export class BaseScene {
  constructor() {
    this.name = '';
    this.sceneManager = null;
    this.isActive = false;
  }

  /**
   * 场景进入时调用
   * @param {Object} params - 传入参数
   */
  onEnter(params = {}) {
    this.isActive = true;
    console.log(`进入场景: ${this.name}`);
  }

  /**
   * 场景退出时调用
   */
  onExit() {
    this.isActive = false;
    console.log(`退出场景: ${this.name}`);
  }

  /**
   * 更新场景逻辑
   * @param {number} deltaTime - 时间间隔
   */
  update(deltaTime) {
    // 子类实现
  }

  /**
   * 渲染场景
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    // 子类实现
  }

  /**
   * 处理触摸事件
   * @param {Object} event - 触摸事件
   */
  handleTouch(event) {
    // 子类实现
  }

  /**
   * 切换到其他场景
   * @param {string} sceneName - 目标场景名称
   * @param {Object} params - 传递参数
   */
  switchScene(sceneName, params = {}) {
    if (this.sceneManager) {
      this.sceneManager.switchTo(sceneName, params);
    }
  }
}
