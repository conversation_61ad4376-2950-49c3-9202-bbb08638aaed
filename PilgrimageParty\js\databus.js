import Pool from './base/pool';

let instance;

/**
 * 全局状态管理器 - 西游记RPG游戏版本
 * 负责管理游戏的状态，包括玩家数据、关卡进度、装备、技能等
 */
export default class DataBus {
  // 游戏基础状态
  frame = 0; // 当前帧数
  isGameOver = false; // 游戏是否结束
  isPaused = false; // 游戏是否暂停
  pool = new Pool(); // 初始化对象池

  // 游戏场景状态
  currentScene = 'menu'; // 当前场景: menu, character_select, battle, inventory, etc.

  // 玩家相关数据
  players = []; // 多人游戏中的玩家列表
  currentPlayer = null; // 当前玩家

  // 关卡系统
  currentChapter = 1; // 当前章节 (1-6)
  currentLevel = 1; // 当前关卡 (1-9)
  maxUnlockedChapter = 1; // 最大解锁章节
  maxUnlockedLevel = 1; // 最大解锁关卡

  // 战斗系统
  battleState = {
    isInBattle: false,
    enemies: [], // 当前战斗中的敌人
    battleTurn: 0, // 战斗回合数
    selectedSkills: [], // 选择的技能
    availableTalents: [] // 可选天赋
  };

  // 游戏内对象
  animations = []; // 存储动画
  effects = []; // 特效

  // 货币系统
  diamonds = 0; // 钻石数量

  // 多人协作
  isMultiplayer = false; // 是否多人模式
  roomId = null; // 房间ID
  teamMembers = []; // 队伍成员

  constructor() {
    // 确保单例模式
    if (instance) return instance;

    // 初始化默认数据
    this.initDefaultData();
    instance = this;
  }

  // 初始化默认数据
  initDefaultData() {
    this.diamonds = 500; // 初始钻石
    this.loadGameData(); // 加载存档数据
  }

  // 重置游戏状态
  reset() {
    this.frame = 0;
    this.isGameOver = false;
    this.isPaused = false;
    this.battleState = {
      isInBattle: false,
      enemies: [],
      battleTurn: 0,
      selectedSkills: [],
      availableTalents: []
    };
    this.animations = [];
    this.effects = [];
  }

  // 游戏结束
  gameOver() {
    this.isGameOver = true;
    this.battleState.isInBattle = false;
  }

  // 场景切换
  switchScene(sceneName) {
    this.currentScene = sceneName;
  }

  // 关卡相关方法
  unlockLevel(chapter, level) {
    if (chapter > this.maxUnlockedChapter ||
        (chapter === this.maxUnlockedChapter && level > this.maxUnlockedLevel + 1)) {
      return false; // 不能跳关
    }

    if (chapter > this.maxUnlockedChapter) {
      this.maxUnlockedChapter = chapter;
      this.maxUnlockedLevel = 1;
    } else if (level > this.maxUnlockedLevel) {
      this.maxUnlockedLevel = level;
    }

    this.saveGameData();
    return true;
  }

  // 进入战斗
  enterBattle(enemies = []) {
    this.battleState.isInBattle = true;
    this.battleState.enemies = enemies;
    this.battleState.battleTurn = 0;
    this.switchScene('battle');
  }

  // 退出战斗
  exitBattle() {
    this.battleState.isInBattle = false;
    this.battleState.enemies = [];
    this.battleState.battleTurn = 0;
    this.switchScene('level_select');
  }

  // 多人游戏相关
  createRoom() {
    this.isMultiplayer = true;
    this.roomId = 'room_' + Date.now();
    return this.roomId;
  }

  joinRoom(roomId) {
    this.isMultiplayer = true;
    this.roomId = roomId;
  }

  leaveRoom() {
    this.isMultiplayer = false;
    this.roomId = null;
    this.teamMembers = [];
  }

  // 数据持久化
  saveGameData() {
    const saveData = {
      maxUnlockedChapter: this.maxUnlockedChapter,
      maxUnlockedLevel: this.maxUnlockedLevel,
      diamonds: this.diamonds,
      currentPlayer: this.currentPlayer
    };

    try {
      wx.setStorageSync('pilgrimagePartyData', saveData);
    } catch (e) {
      console.error('保存游戏数据失败:', e);
    }
  }

  // 加载游戏数据
  loadGameData() {
    try {
      const saveData = wx.getStorageSync('pilgrimagePartyData');
      if (saveData) {
        this.maxUnlockedChapter = saveData.maxUnlockedChapter || 1;
        this.maxUnlockedLevel = saveData.maxUnlockedLevel || 1;
        this.diamonds = saveData.diamonds || 500;
        this.currentPlayer = saveData.currentPlayer || null;
      }
    } catch (e) {
      console.error('加载游戏数据失败:', e);
    }
  }

  // 回收敌人
  removeEnemy(enemy) {
    const index = this.battleState.enemies.indexOf(enemy);
    if (index > -1) {
      this.battleState.enemies.splice(index, 1);
      this.pool.recover('enemy', enemy);
    }
  }

  // 回收特效
  removeEffect(effect) {
    const index = this.effects.indexOf(effect);
    if (index > -1) {
      this.effects.splice(index, 1);
      this.pool.recover('effect', effect);
    }
  }
}
