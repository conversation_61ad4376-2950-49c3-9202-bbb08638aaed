import { BaseScene } from '../core/SceneManager';
import { SCENES, CHARACTERS } from '../config/GameConfig';

/**
 * 主菜单场景
 */
export default class MenuScene extends BaseScene {
  constructor() {
    super();
    this.buttons = []; // 按钮列表
    this.title = '一起来取经'; // 游戏标题
    this.initButtons();
  }

  /**
   * 初始化按钮
   */
  initButtons() {
    const buttonWidth = 200;
    const buttonHeight = 60;
    const centerX = canvas.width / 2;
    const startY = 300;
    const spacing = 80;

    this.buttons = [
      {
        text: '开始游戏',
        x: centerX - buttonWidth / 2,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        action: () => this.switchScene(SCENES.CHARACTER_SELECT)
      },
      {
        text: '多人游戏',
        x: centerX - buttonWidth / 2,
        y: startY + spacing,
        width: buttonWidth,
        height: buttonHeight,
        action: () => this.switchScene(SCENES.MULTIPLAYER_LOBBY)
      },
      {
        text: '装备背包',
        x: centerX - buttonWidth / 2,
        y: startY + spacing * 2,
        width: buttonWidth,
        height: buttonHeight,
        action: () => this.switchScene(SCENES.INVENTORY)
      },
      {
        text: '商店',
        x: centerX - buttonWidth / 2,
        y: startY + spacing * 3,
        width: buttonWidth,
        height: buttonHeight,
        action: () => this.switchScene(SCENES.SHOP)
      }
    ];
  }

  /**
   * 场景进入时调用
   */
  onEnter(params = {}) {
    super.onEnter(params);
    
    // 播放主菜单背景音乐
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playBgm('menu');
    }
    
    console.log('进入主菜单场景');
  }

  /**
   * 场景退出时调用
   */
  onExit() {
    super.onExit();
    console.log('退出主菜单场景');
  }

  /**
   * 更新场景逻辑
   */
  update(deltaTime) {
    // 主菜单暂时不需要复杂的更新逻辑
  }

  /**
   * 渲染场景
   */
  render(ctx) {
    // 绘制背景
    this.renderBackground(ctx);
    
    // 绘制标题
    this.renderTitle(ctx);
    
    // 绘制按钮
    this.renderButtons(ctx);
    
    // 绘制游戏信息
    this.renderGameInfo(ctx);
  }

  /**
   * 绘制背景
   */
  renderBackground(ctx) {
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#87CEEB'); // 天蓝色
    gradient.addColorStop(1, '#FFE4B5'); // 浅黄色
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 可以在这里添加背景图片
    // if (this.backgroundImage) {
    //   ctx.drawImage(this.backgroundImage, 0, 0, canvas.width, canvas.height);
    // }
  }

  /**
   * 绘制标题
   */
  renderTitle(ctx) {
    ctx.save();
    
    // 设置标题样式
    ctx.fillStyle = '#8B4513'; // 棕色
    ctx.font = 'bold 48px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // 绘制标题阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
    ctx.shadowOffsetX = 3;
    ctx.shadowOffsetY = 3;
    ctx.shadowBlur = 5;
    
    ctx.fillText(this.title, canvas.width / 2, 150);
    
    ctx.restore();
  }

  /**
   * 绘制按钮
   */
  renderButtons(ctx) {
    this.buttons.forEach(button => {
      this.renderButton(ctx, button);
    });
  }

  /**
   * 绘制单个按钮
   */
  renderButton(ctx, button) {
    ctx.save();
    
    // 绘制按钮背景
    ctx.fillStyle = '#DEB887'; // 浅棕色
    ctx.strokeStyle = '#8B4513'; // 深棕色边框
    ctx.lineWidth = 3;
    
    // 绘制圆角矩形
    this.drawRoundedRect(ctx, button.x, button.y, button.width, button.height, 10);
    ctx.fill();
    ctx.stroke();
    
    // 绘制按钮文字
    ctx.fillStyle = '#8B4513';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText(
      button.text,
      button.x + button.width / 2,
      button.y + button.height / 2
    );
    
    ctx.restore();
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }

  /**
   * 绘制游戏信息
   */
  renderGameInfo(ctx) {
    ctx.save();
    
    // 显示钻石数量
    ctx.fillStyle = '#FFD700'; // 金色
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'top';
    
    ctx.fillText(`钻石: ${GameGlobal.databus.diamonds}`, canvas.width - 20, 20);
    
    // 显示版本信息
    ctx.fillStyle = '#666666';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'bottom';
    
    ctx.fillText('v1.0.0', 20, canvas.height - 20);
    
    ctx.restore();
  }

  /**
   * 处理触摸事件
   */
  handleTouch(event) {
    if (event.type === 'touchstart' || event.type === 'click') {
      // 检查是否点击了按钮
      this.buttons.forEach(button => {
        if (this.isPointInButton(event.x, event.y, button)) {
          // 播放点击音效
          if (GameGlobal.musicManager) {
            GameGlobal.musicManager.playSfx('click');
          }
          
          // 执行按钮动作
          button.action();
        }
      });
    }
  }

  /**
   * 检查点是否在按钮内
   */
  isPointInButton(x, y, button) {
    return x >= button.x && 
           x <= button.x + button.width && 
           y >= button.y && 
           y <= button.y + button.height;
  }
}
