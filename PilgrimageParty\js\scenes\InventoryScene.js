import { BaseScene } from '../core/SceneManager';
import { SCENES, EQUIPMENT } from '../config/GameConfig';

/**
 * 背包/装备场景
 */
export default class InventoryScene extends BaseScene {
  constructor() {
    super();
    this.currentTab = 'equipment'; // equipment, items, gems
    this.selectedItem = null;
  }

  /**
   * 场景进入时调用
   */
  onEnter(params = {}) {
    super.onEnter(params);
    console.log('进入背包场景');
  }

  /**
   * 更新场景逻辑
   */
  update(deltaTime) {
    // 背包场景的更新逻辑
  }

  /**
   * 渲染场景
   */
  render(ctx) {
    this.renderBackground(ctx);
    this.renderTitle(ctx);
    this.renderTabs(ctx);
    this.renderContent(ctx);
    this.renderBackButton(ctx);
  }

  /**
   * 绘制背景
   */
  renderBackground(ctx) {
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#2F4F4F');
    gradient.addColorStop(1, '#708090');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }

  /**
   * 绘制标题
   */
  renderTitle(ctx) {
    ctx.save();
    ctx.fillStyle = 'white';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('装备背包', canvas.width / 2, 60);
    ctx.restore();
  }

  /**
   * 绘制标签页
   */
  renderTabs(ctx) {
    const tabs = [
      { id: 'equipment', name: '装备' },
      { id: 'items', name: '道具' },
      { id: 'gems', name: '宝石' }
    ];

    const tabWidth = 100;
    const tabHeight = 40;
    const startX = (canvas.width - tabs.length * tabWidth) / 2;
    const tabY = 100;

    tabs.forEach((tab, index) => {
      const tabX = startX + index * tabWidth;
      const isActive = tab.id === this.currentTab;

      ctx.save();
      
      // 绘制标签背景
      ctx.fillStyle = isActive ? '#4169E1' : '#696969';
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;
      
      ctx.fillRect(tabX, tabY, tabWidth, tabHeight);
      ctx.strokeRect(tabX, tabY, tabWidth, tabHeight);
      
      // 绘制标签文字
      ctx.fillStyle = 'white';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      ctx.fillText(tab.name, tabX + tabWidth / 2, tabY + tabHeight / 2);
      
      ctx.restore();
    });
  }

  /**
   * 绘制内容区域
   */
  renderContent(ctx) {
    switch (this.currentTab) {
      case 'equipment':
        this.renderEquipmentContent(ctx);
        break;
      case 'items':
        this.renderItemsContent(ctx);
        break;
      case 'gems':
        this.renderGemsContent(ctx);
        break;
    }
  }

  /**
   * 绘制装备内容
   */
  renderEquipmentContent(ctx) {
    const player = GameGlobal.databus.currentPlayer;
    if (!player) return;

    // 绘制装备槽位
    const slots = [
      { type: 'helmet', name: '头盔', x: canvas.width / 2 - 30, y: 180 },
      { type: 'armor', name: '衣服', x: canvas.width / 2 - 30, y: 250 },
      { type: 'leggings', name: '护腿', x: canvas.width / 2 - 30, y: 320 },
      { type: 'boots', name: '鞋子', x: canvas.width / 2 - 30, y: 390 }
    ];

    slots.forEach(slot => {
      this.renderEquipmentSlot(ctx, slot, player.equipment[slot.type]);
    });

    // 绘制角色属性
    this.renderCharacterStats(ctx, player);
  }

  /**
   * 绘制装备槽位
   */
  renderEquipmentSlot(ctx, slot, equipment) {
    ctx.save();
    
    const slotSize = 60;
    
    // 绘制槽位背景
    ctx.fillStyle = equipment ? '#4169E1' : '#696969';
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    
    ctx.fillRect(slot.x, slot.y, slotSize, slotSize);
    ctx.strokeRect(slot.x, slot.y, slotSize, slotSize);
    
    // 绘制槽位名称
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    
    ctx.fillText(slot.name, slot.x + slotSize + 10, slot.y + slotSize / 2);
    
    // 如果有装备，绘制装备信息
    if (equipment) {
      ctx.font = '12px Arial';
      ctx.fillText(equipment.name, slot.x + slotSize + 10, slot.y + slotSize / 2 + 15);
    }
    
    ctx.restore();
  }

  /**
   * 绘制角色属性
   */
  renderCharacterStats(ctx, player) {
    ctx.save();
    
    const statsX = 50;
    const statsY = 500;
    const stats = player.character.baseStats;
    
    ctx.fillStyle = 'white';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    
    ctx.fillText(`生命值: ${stats.hp}`, statsX, statsY);
    ctx.fillText(`攻击力: ${stats.attack}`, statsX, statsY + 25);
    ctx.fillText(`防御力: ${stats.defense}`, statsX, statsY + 50);
    ctx.fillText(`速度: ${stats.speed}`, statsX, statsY + 75);
    
    ctx.restore();
  }

  /**
   * 绘制道具内容
   */
  renderItemsContent(ctx) {
    ctx.save();
    
    ctx.fillStyle = 'white';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('道具系统开发中...', canvas.width / 2, 300);
    
    ctx.restore();
  }

  /**
   * 绘制宝石内容
   */
  renderGemsContent(ctx) {
    ctx.save();
    
    ctx.fillStyle = 'white';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('宝石系统开发中...', canvas.width / 2, 300);
    
    ctx.restore();
  }

  /**
   * 绘制返回按钮
   */
  renderBackButton(ctx) {
    ctx.save();
    
    const buttonX = 20;
    const buttonY = 20;
    const buttonWidth = 80;
    const buttonHeight = 40;
    
    ctx.fillStyle = '#DC143C';
    ctx.strokeStyle = '#8B0000';
    ctx.lineWidth = 2;
    
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
    ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);
    
    ctx.fillStyle = 'white';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('返回', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);
    
    ctx.restore();
  }

  /**
   * 处理触摸事件
   */
  handleTouch(event) {
    if (event.type === 'touchstart' || event.type === 'click') {
      // 检查标签页点击
      this.checkTabClick(event.x, event.y);
      
      // 检查返回按钮点击
      if (this.isPointInRect(event.x, event.y, {
        x: 20,
        y: 20,
        width: 80,
        height: 40
      })) {
        this.switchScene(SCENES.MENU);
      }
    }
  }

  /**
   * 检查标签页点击
   */
  checkTabClick(x, y) {
    const tabs = ['equipment', 'items', 'gems'];
    const tabWidth = 100;
    const tabHeight = 40;
    const startX = (canvas.width - tabs.length * tabWidth) / 2;
    const tabY = 100;

    tabs.forEach((tabId, index) => {
      const tabX = startX + index * tabWidth;
      
      if (this.isPointInRect(x, y, {
        x: tabX,
        y: tabY,
        width: tabWidth,
        height: tabHeight
      })) {
        this.currentTab = tabId;
        
        if (GameGlobal.musicManager) {
          GameGlobal.musicManager.playSfx('click');
        }
      }
    });
  }

  /**
   * 检查点是否在矩形内
   */
  isPointInRect(x, y, rect) {
    return x >= rect.x && 
           x <= rect.x + rect.width && 
           y >= rect.y && 
           y <= rect.y + rect.height;
  }
}
