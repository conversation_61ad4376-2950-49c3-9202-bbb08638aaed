import { BaseScene } from '../core/SceneManager';
import { SCENES, CHAPTERS } from '../config/GameConfig';

/**
 * 关卡选择场景
 */
export default class LevelSelectScene extends BaseScene {
  constructor() {
    super();
    this.currentChapter = 1;
    this.levelButtons = [];
    this.initLevelButtons();
  }

  /**
   * 初始化关卡按钮
   */
  initLevelButtons() {
    const chapter = CHAPTERS[this.currentChapter];
    if (!chapter) return;

    const buttonSize = 60;
    const spacing = 10;
    const cols = 3;
    const startX = (canvas.width - (cols * buttonSize + (cols - 1) * spacing)) / 2;
    const startY = 200;

    this.levelButtons = chapter.levels.map((level, index) => {
      const row = Math.floor(index / cols);
      const col = index % cols;
      
      return {
        level: level,
        x: startX + col * (buttonSize + spacing),
        y: startY + row * (buttonSize + spacing),
        width: buttonSize,
        height: buttonSize,
        unlocked: this.isLevelUnlocked(level.id)
      };
    });
  }

  /**
   * 检查关卡是否解锁
   */
  isLevelUnlocked(levelId) {
    const databus = GameGlobal.databus;
    const chapter = Math.ceil(levelId / 9);
    const levelInChapter = ((levelId - 1) % 9) + 1;
    
    return chapter < databus.maxUnlockedChapter || 
           (chapter === databus.maxUnlockedChapter && levelInChapter <= databus.maxUnlockedLevel);
  }

  /**
   * 场景进入时调用
   */
  onEnter(params = {}) {
    super.onEnter(params);
    this.initLevelButtons();
    console.log('进入关卡选择场景');
  }

  /**
   * 更新场景逻辑
   */
  update(deltaTime) {
    // 关卡选择场景的更新逻辑
  }

  /**
   * 渲染场景
   */
  render(ctx) {
    this.renderBackground(ctx);
    this.renderTitle(ctx);
    this.renderChapterInfo(ctx);
    this.renderLevels(ctx);
    this.renderBackButton(ctx);
  }

  /**
   * 绘制背景
   */
  renderBackground(ctx) {
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#87CEEB');
    gradient.addColorStop(1, '#F0E68C');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }

  /**
   * 绘制标题
   */
  renderTitle(ctx) {
    ctx.save();
    ctx.fillStyle = '#8B4513';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('选择关卡', canvas.width / 2, 80);
    ctx.restore();
  }

  /**
   * 绘制章节信息
   */
  renderChapterInfo(ctx) {
    const chapter = CHAPTERS[this.currentChapter];
    if (!chapter) return;

    ctx.save();
    ctx.fillStyle = '#8B4513';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText(`第${this.currentChapter}章: ${chapter.name}`, canvas.width / 2, 140);
    ctx.restore();
  }

  /**
   * 绘制关卡
   */
  renderLevels(ctx) {
    this.levelButtons.forEach(button => {
      this.renderLevelButton(ctx, button);
    });
  }

  /**
   * 绘制关卡按钮
   */
  renderLevelButton(ctx, button) {
    ctx.save();
    
    // 设置按钮颜色
    if (button.unlocked) {
      ctx.fillStyle = '#32CD32'; // 绿色 - 已解锁
      ctx.strokeStyle = '#228B22';
    } else {
      ctx.fillStyle = '#808080'; // 灰色 - 未解锁
      ctx.strokeStyle = '#696969';
    }
    
    ctx.lineWidth = 3;
    
    // 绘制圆形按钮
    const centerX = button.x + button.width / 2;
    const centerY = button.y + button.height / 2;
    const radius = button.width / 2 - 5;
    
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 绘制关卡编号
    ctx.fillStyle = 'white';
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const levelNumber = ((button.level.id - 1) % 9) + 1;
    ctx.fillText(levelNumber.toString(), centerX, centerY);
    
    // 绘制关卡名称
    ctx.fillStyle = '#8B4513';
    ctx.font = '12px Arial';
    ctx.fillText(button.level.name, centerX, centerY + radius + 15);
    
    ctx.restore();
  }

  /**
   * 绘制返回按钮
   */
  renderBackButton(ctx) {
    ctx.save();
    
    const buttonX = 20;
    const buttonY = 20;
    const buttonWidth = 80;
    const buttonHeight = 40;
    
    ctx.fillStyle = '#DC143C';
    ctx.strokeStyle = '#8B0000';
    ctx.lineWidth = 2;
    
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
    ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);
    
    ctx.fillStyle = 'white';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    ctx.fillText('返回', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);
    
    ctx.restore();
  }

  /**
   * 处理触摸事件
   */
  handleTouch(event) {
    if (event.type === 'touchstart' || event.type === 'click') {
      // 检查关卡按钮点击
      this.levelButtons.forEach(button => {
        if (button.unlocked && this.isPointInCircle(event.x, event.y, button)) {
          // 进入战斗场景
          this.switchScene(SCENES.BATTLE, {
            chapter: this.currentChapter,
            level: button.level
          });
          
          if (GameGlobal.musicManager) {
            GameGlobal.musicManager.playSfx('click');
          }
        }
      });
      
      // 检查返回按钮点击
      if (this.isPointInRect(event.x, event.y, {
        x: 20,
        y: 20,
        width: 80,
        height: 40
      })) {
        this.switchScene(SCENES.CHARACTER_SELECT);
      }
    }
  }

  /**
   * 检查点是否在圆形按钮内
   */
  isPointInCircle(x, y, button) {
    const centerX = button.x + button.width / 2;
    const centerY = button.y + button.height / 2;
    const radius = button.width / 2 - 5;
    
    const distance = Math.sqrt(
      Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
    );
    
    return distance <= radius;
  }

  /**
   * 检查点是否在矩形内
   */
  isPointInRect(x, y, rect) {
    return x >= rect.x && 
           x <= rect.x + rect.width && 
           y >= rect.y && 
           y <= rect.y + rect.height;
  }
}
