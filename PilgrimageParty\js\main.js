import './render'; // 初始化Canvas
import DataBus from './databus'; // 导入数据类，用于管理游戏状态和数据
import SceneManager from './core/SceneManager'; // 导入场景管理器
import Music from './runtime/music'; // 导入音乐类

// 导入各种场景
import MenuScene from './scenes/MenuScene';
import CharacterSelectScene from './scenes/CharacterSelectScene';
import LevelSelectScene from './scenes/LevelSelectScene';
import BattleScene from './scenes/BattleScene';
import InventoryScene from './scenes/InventoryScene';

import { SCENES, GAME_CONFIG } from './config/GameConfig'; // 导入配置

const ctx = canvas.getContext('2d'); // 获取canvas的2D绘图上下文

GameGlobal.databus = new DataBus(); // 全局数据管理
GameGlobal.musicManager = new Music(); // 全局音乐管理实例

/**
 * 游戏主函数 - 西游记RPG版本
 */
export default class Main {
  aniId = 0; // 用于存储动画帧的ID
  sceneManager = new SceneManager(); // 场景管理器
  lastTime = 0; // 上一帧时间
  deltaTime = 0; // 时间间隔

  constructor() {
    this.initScenes(); // 初始化所有场景
    this.bindEvents(); // 绑定事件
    this.start(); // 开始游戏
  }

  /**
   * 初始化所有游戏场景
   */
  initScenes() {
    // 注册所有场景
    this.sceneManager.registerScene(SCENES.MENU, new MenuScene());
    this.sceneManager.registerScene(SCENES.CHARACTER_SELECT, new CharacterSelectScene());
    this.sceneManager.registerScene(SCENES.LEVEL_SELECT, new LevelSelectScene());
    this.sceneManager.registerScene(SCENES.BATTLE, new BattleScene());
    this.sceneManager.registerScene(SCENES.INVENTORY, new InventoryScene());
  }

  /**
   * 绑定游戏事件
   */
  bindEvents() {
    // 绑定触摸事件
    canvas.addEventListener('touchstart', this.handleTouch.bind(this));
    canvas.addEventListener('touchmove', this.handleTouch.bind(this));
    canvas.addEventListener('touchend', this.handleTouch.bind(this));

    // 绑定点击事件（用于调试）
    canvas.addEventListener('click', this.handleTouch.bind(this));
  }

  /**
   * 开始游戏
   */
  start() {
    GameGlobal.databus.reset(); // 重置数据
    this.lastTime = performance.now(); // 初始化时间

    // 切换到主菜单场景
    this.sceneManager.switchTo(SCENES.MENU);

    cancelAnimationFrame(this.aniId); // 清除上一局的动画
    this.aniId = requestAnimationFrame(this.loop.bind(this)); // 开始新的动画循环
  }

  /**
   * 处理触摸事件
   * @param {TouchEvent|MouseEvent} event - 触摸或鼠标事件
   */
  handleTouch(event) {
    event.preventDefault();

    // 获取触摸位置
    let x, y;
    if (event.touches && event.touches.length > 0) {
      const touch = event.touches[0];
      const rect = canvas.getBoundingClientRect();
      x = touch.clientX - rect.left;
      y = touch.clientY - rect.top;
    } else if (event.clientX !== undefined) {
      const rect = canvas.getBoundingClientRect();
      x = event.clientX - rect.left;
      y = event.clientY - rect.top;
    }

    // 转换为游戏坐标
    const gameX = (x / canvas.clientWidth) * GAME_CONFIG.CANVAS_WIDTH;
    const gameY = (y / canvas.clientHeight) * GAME_CONFIG.CANVAS_HEIGHT;

    // 传递给场景管理器处理
    this.sceneManager.handleTouch({
      type: event.type,
      x: gameX,
      y: gameY,
      originalEvent: event
    });
  }

  /**
   * canvas重绘函数
   * 每一帧重新绘制所有的需要展示的元素
   */
  render() {
    ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布

    // 渲染当前场景
    this.sceneManager.render(ctx);

    // 渲染全局动画和特效
    GameGlobal.databus.animations.forEach((ani) => {
      if (ani.isPlaying) {
        ani.aniRender(ctx); // 渲染动画
      }
    });

    GameGlobal.databus.effects.forEach((effect) => {
      if (effect.isActive) {
        effect.render(ctx); // 渲染特效
      }
    });
  }

  /**
   * 游戏逻辑更新主函数
   */
  update() {
    const currentTime = performance.now();
    this.deltaTime = currentTime - this.lastTime;
    this.lastTime = currentTime;

    GameGlobal.databus.frame++; // 增加帧数

    // 如果游戏暂停，不更新逻辑
    if (GameGlobal.databus.isPaused) {
      return;
    }

    // 更新场景管理器
    this.sceneManager.update(this.deltaTime);

    // 更新全局动画
    GameGlobal.databus.animations.forEach((ani, index) => {
      if (ani.isPlaying) {
        ani.update(this.deltaTime);
      } else {
        // 移除已完成的动画
        GameGlobal.databus.animations.splice(index, 1);
      }
    });

    // 更新全局特效
    GameGlobal.databus.effects.forEach((effect, index) => {
      if (effect.isActive) {
        effect.update(this.deltaTime);
      } else {
        // 移除已完成的特效
        GameGlobal.databus.effects.splice(index, 1);
      }
    });
  }

  /**
   * 实现游戏帧循环
   */
  loop() {
    this.update(); // 更新游戏逻辑
    this.render(); // 渲染游戏画面

    // 请求下一帧动画
    this.aniId = requestAnimationFrame(this.loop.bind(this));
  }

  /**
   * 销毁游戏实例
   */
  destroy() {
    cancelAnimationFrame(this.aniId);
    this.sceneManager.destroy();

    // 清理事件监听
    canvas.removeEventListener('touchstart', this.handleTouch);
    canvas.removeEventListener('touchmove', this.handleTouch);
    canvas.removeEventListener('touchend', this.handleTouch);
    canvas.removeEventListener('click', this.handleTouch);
  }
}
